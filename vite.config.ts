import { reactRouter } from "@react-router/dev/vite";
import tailwindcss from "@tailwindcss/vite";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { readFileSync } from "fs";
import { join } from "path";

export default defineConfig({
  plugins: [
    tailwindcss(),
    reactRouter(),
    tsconfigPaths(),
    // Custom plugin to serve service workers
    {
      name: "service-worker-middleware",
      configureServer(server) {
        server.middlewares.use(
          "/firebase-messaging-sw.js",
          (req, res, next) => {
            try {
              const swPath = join(
                process.cwd(),
                "public",
                "firebase-messaging-sw.js"
              );
              const swContent = readFileSync(swPath, "utf-8");
              res.setHeader("Content-Type", "application/javascript");
              res.setHeader("Service-Worker-Allowed", "/");
              res.setHeader(
                "Cache-Control",
                "no-cache, no-store, must-revalidate"
              );
              res.end(swContent);
            } catch (error) {
              console.error("Failed to serve Firebase service worker:", error);
              res.statusCode = 404;
              res.end("Service worker not found");
            }
          }
        );
      },
    },
  ],
  define: {
    global: "globalThis",
    "process.env": {},
  },
  publicDir: "public",
  server: {
    fs: {
      allow: [".."],
    },
    proxy: {
      "/auth-api": {
        target: "https://api.slosphere.com",
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/auth-api/, "/auth"),
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.error("[proxy] proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log(
              "[proxy] Sending Request to the Target:",
              req.method,
              req.url
            );
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log(
              "[proxy] Received Response from the Target:",
              proxyRes.statusCode,
              req.url
            );
          });
        },
      },
      "/api": {
        target: "https://api.slosphere.com",
        changeOrigin: true,
        secure: false,
        ws: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.error("[proxy] Proxy error", err);
          });
          proxy.on("proxyReq", (proxyReq, req, _res) => {
            console.log(
              "[proxy] Sending Request to the Target:",
              req.method,
              req.url
            );
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log(
              "[proxy] Received Response from the Target:",
              proxyRes.statusCode,
              req.url
            );
          });
        },
      },
    },
  },
});
