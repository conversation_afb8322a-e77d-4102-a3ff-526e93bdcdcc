// Sphere Web Service Worker for Firebase Push Notifications
import { initializeApp } from "firebase/app";
import { getMessaging, onBackgroundMessage } from "firebase/messaging/sw";

const CACHE_NAME = "sphere-web-v1";
const NOTIFICATION_TAG = "sphere-notification";

// Firebase configuration will be set by the main app
let firebaseApp = null;
let messaging = null;

// Initialize Firebase when config is received from main app
function initializeFirebase(config) {
  try {
    console.log("[SW] Initializing Firebase with config:", {
      projectId: config.projectId,
    });
    firebaseApp = initializeApp(config);
    messaging = getMessaging(firebaseApp);
    setupBackgroundMessageHandler();
    console.log("[SW] Firebase initialized successfully");
  } catch (error) {
    console.error("[SW] Failed to initialize Firebase:", error);
  }
}

function setupBackgroundMessageHandler() {
  if (!messaging) return;

  // Handle background messages from Firebase
  onBackgroundMessage(messaging, (payload) => {
    console.log("[SW] Received background message:", payload);

    const notificationTitle = payload.notification?.title || "Sphere";
    const notificationOptions = {
      body: payload.notification?.body || "You have a new notification",
      icon: payload.notification?.icon || "/favicon.ico",
      badge: "/favicon.ico",
      tag: NOTIFICATION_TAG,
      data: {
        ...payload.data,
        timestamp: Date.now(),
      },
    };

    // Add action buttons based on notification type
    if (payload.data?.type === "chat") {
      notificationOptions.actions = [
        { action: "reply", title: "Reply", icon: "/favicon.ico" },
        { action: "view", title: "View Chat", icon: "/favicon.ico" },
      ];
    } else if (payload.data?.type === "lesson") {
      notificationOptions.actions = [
        { action: "view", title: "Start Lesson", icon: "/favicon.ico" },
        { action: "later", title: "Remind Later", icon: "/favicon.ico" },
      ];
    } else if (payload.data?.type === "live_class") {
      notificationOptions.actions = [
        { action: "join", title: "Join Now", icon: "/favicon.ico" },
        { action: "view", title: "View Details", icon: "/favicon.ico" },
      ];
    }

    console.log("[SW] Showing notification:", notificationTitle);
    return self.registration.showNotification(
      notificationTitle,
      notificationOptions
    );
  });
}

// Install event - cache essential resources
self.addEventListener("install", (event) => {
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll([
        "/",
        "/favicon.ico",
        // Add other essential resources as needed
      ]);
    })
  );
  // Take control immediately
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener("activate", (event) => {
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  // Take control of all clients
  self.clients.claim();
});

// Push event - handle incoming push notifications
self.addEventListener("push", (event) => {
  let notificationData = {
    title: "Sphere",
    body: "You have a new notification",
    icon: "/favicon.ico",
    badge: "/favicon.ico",
    tag: NOTIFICATION_TAG,
    data: {
      url: "/",
      timestamp: Date.now(),
    },
  };

  // Parse push data if available
  if (event.data) {
    try {
      const pushData = event.data.json();
      notificationData = {
        ...notificationData,
        title: pushData.title || notificationData.title,
        body: pushData.body || notificationData.body,
        icon: pushData.icon || notificationData.icon,
        data: {
          ...notificationData.data,
          ...pushData.data,
          url: pushData.url || pushData.data?.link || notificationData.data.url,
          notificationId: pushData.notificationId,
          type: pushData.type,
        },
      };

      // Add action buttons based on notification type
      if (pushData.type === "chat") {
        notificationData.actions = [
          { action: "reply", title: "Reply", icon: "/favicon.ico" },
          { action: "view", title: "View Chat", icon: "/favicon.ico" },
        ];
      } else if (pushData.type === "lesson") {
        notificationData.actions = [
          { action: "view", title: "Start Lesson", icon: "/favicon.ico" },
          { action: "later", title: "Remind Later", icon: "/favicon.ico" },
        ];
      } else if (pushData.type === "live_class") {
        notificationData.actions = [
          { action: "join", title: "Join Now", icon: "/favicon.ico" },
          { action: "view", title: "View Details", icon: "/favicon.ico" },
        ];
      }
    } catch (error) {
      console.error("[SW] Error parsing push data:", error);
    }
  }

  event.waitUntil(
    self.registration.showNotification(notificationData.title, notificationData)
  );
});

// Notification click event - handle user interactions
self.addEventListener("notificationclick", (event) => {
  console.log("[SW] Notification clicked:", {
    title: event.notification.title,
    action: event.action,
    data: event.notification.data,
  });

  const notification = event.notification;
  const action = event.action;
  const data = notification.data || {};

  notification.close();

  // Handle different actions
  if (action === "later") {
    // Schedule a reminder (could be implemented with setTimeout or backend API)
    return;
  }

  // Determine the URL to open
  let urlToOpen = data.url || "/";

  // Handle specific notification types
  if (data.type === "chat" && action === "reply") {
    urlToOpen = "/chat";
  } else if (data.type === "lesson") {
    urlToOpen = data.url || "/";
  } else if (data.type === "live_class" && action === "join") {
    urlToOpen = data.url || "/";
  }

  // Focus existing window or open new one
  event.waitUntil(
    clients
      .matchAll({ type: "window", includeUncontrolled: true })
      .then((clientList) => {
        // Check if there's already a window open
        for (const client of clientList) {
          if (client.url.includes(self.location.origin)) {
            // Focus the existing window and navigate
            return client.focus().then(() => {
              return client.postMessage({
                type: "NOTIFICATION_CLICK",
                url: urlToOpen,
                notificationData: data,
              });
            });
          }
        }

        // No existing window, open a new one
        return clients.openWindow(urlToOpen);
      })
  );
});

// Background sync for offline actions
self.addEventListener("sync", (event) => {
  if (event.tag === "notification-read") {
    event.waitUntil(syncNotificationReads());
  }
});

// Sync notification read status when back online
async function syncNotificationReads() {
  try {
    // Get pending read notifications from IndexedDB or localStorage
    const pendingReads = await getPendingNotificationReads();

    for (const notificationId of pendingReads) {
      try {
        // Attempt to sync with backend
        await fetch(`/api/notifications/${notificationId}/read`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        // Remove from pending list on success
        await removePendingNotificationRead(notificationId);
      } catch (error) {
        console.error("[SW] Failed to sync notification read:", error);
      }
    }
  } catch (error) {
    console.error("[SW] Background sync failed:", error);
  }
}

// Helper functions for offline storage
async function getPendingNotificationReads() {
  // Implementation would use IndexedDB or localStorage
  return [];
}

async function removePendingNotificationRead(notificationId) {
  // Implementation would remove from IndexedDB or localStorage
}

// Message handling from main thread
self.addEventListener("message", (event) => {
  if (event.data && event.data.type === "SKIP_WAITING") {
    self.skipWaiting();
  } else if (event.data && event.data.type === "FIREBASE_CONFIG") {
    console.log("[SW] Received Firebase config from main app");
    initializeFirebase(event.data.config);
  }
});

// Error handling
self.addEventListener("error", (event) => {
  console.error("[SW] Service worker error:", event.error);
});

self.addEventListener("unhandledrejection", (event) => {
  console.error("[SW] Unhandled promise rejection:", event.reason);
});
