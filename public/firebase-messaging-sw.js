// Firebase Cloud Messaging Service Worker
// This file is specifically for Firebase messaging and is required to be at this exact path

importScripts(
  "https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"
);
importScripts(
  "https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js"
);

// Firebase configuration will be set by the main app
let firebaseApp = null;
let messaging = null;

// Initialize Firebase when config is received from main app
function initializeFirebase(config) {
  try {
    console.log("[Firebase SW] Initializing Firebase with config:", {
      projectId: config.projectId,
    });
    firebaseApp = firebase.initializeApp(config);
    messaging = firebase.messaging();
    setupBackgroundMessageHandler();
    console.log("[Firebase SW] Firebase initialized successfully");
  } catch (error) {
    console.error("[Firebase SW] Failed to initialize Firebase:", error);
  }
}

function setupBackgroundMessageHandler() {
  if (!messaging) return;

  // Handle background messages from Firebase
  messaging.onBackgroundMessage((payload) => {
    console.log("[Firebase SW] Received background message:", payload);

    const notificationTitle = payload.notification?.title || "Sphere";
    const notificationOptions = {
      body: payload.notification?.body || "You have a new notification",
      icon: payload.notification?.icon || "/favicon.ico",
      badge: "/favicon.ico",
      tag: "sphere-notification",
      data: {
        ...payload.data,
        timestamp: Date.now(),
      },
    };

    // Add action buttons based on notification type
    if (payload.data?.type === "chat") {
      notificationOptions.actions = [
        { action: "reply", title: "Reply", icon: "/favicon.ico" },
        { action: "view", title: "View Chat", icon: "/favicon.ico" },
      ];
    } else if (payload.data?.type === "lesson") {
      notificationOptions.actions = [
        { action: "view", title: "Start Lesson", icon: "/favicon.ico" },
        { action: "later", title: "Remind Later", icon: "/favicon.ico" },
      ];
    } else if (payload.data?.type === "live_class") {
      notificationOptions.actions = [
        { action: "join", title: "Join Now", icon: "/favicon.ico" },
        { action: "view", title: "View Details", icon: "/favicon.ico" },
      ];
    }

    console.log("[Firebase SW] Showing notification:", notificationTitle);
    return self.registration.showNotification(
      notificationTitle,
      notificationOptions
    );
  });
}

// Notification click event - handle user interactions
self.addEventListener("notificationclick", (event) => {
  console.log("[Firebase SW] Notification clicked:", {
    title: event.notification.title,
    action: event.action,
    data: event.notification.data,
  });

  const notification = event.notification;
  const action = event.action;
  const data = notification.data || {};

  notification.close();

  // Handle different actions
  if (action === "later") {
    // Schedule a reminder (could be implemented with setTimeout or backend API)
    return;
  }

  // Determine the URL to open
  let urlToOpen = data.url || "/";

  // Handle specific notification types
  if (data.type === "chat" && action === "reply") {
    urlToOpen = "/chat";
  } else if (data.type === "lesson") {
    urlToOpen = data.url || "/";
  } else if (data.type === "live_class" && action === "join") {
    urlToOpen = data.url || "/";
  }

  // Focus existing window or open new one
  event.waitUntil(
    clients
      .matchAll({ type: "window", includeUncontrolled: true })
      .then((clientList) => {
        // Check if there's already a window open
        for (const client of clientList) {
          if (client.url.includes(self.location.origin)) {
            // Focus the existing window and navigate
            return client.focus().then(() => {
              return client.postMessage({
                type: "NOTIFICATION_CLICK",
                url: urlToOpen,
                notificationData: data,
              });
            });
          }
        }

        // No existing window, open a new one
        return clients.openWindow(urlToOpen);
      })
  );
});

// Message handling from main thread
self.addEventListener("message", (event) => {
  if (event.data && event.data.type === "FIREBASE_CONFIG") {
    console.log("[Firebase SW] Received Firebase config from main app");
    initializeFirebase(event.data.config);
  }
});

// Error handling
self.addEventListener("error", (event) => {
  console.error("[Firebase SW] Service worker error:", event.error);
});

self.addEventListener("unhandledrejection", (event) => {
  console.error("[Firebase SW] Unhandled promise rejection:", event.reason);
});
