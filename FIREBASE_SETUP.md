# Firebase Push Notifications Setup

This guide explains how to set up Firebase Cloud Messaging (FCM) for push notifications in Sphere Web.

## 🔥 Why Firebase?

Since your Go backend already uses Firebase, using the Firebase SDK provides:
- ✅ **Seamless integration** with your existing Firebase setup
- ✅ **Automatic token management** and refresh
- ✅ **Better reliability** with built-in retry logic
- ✅ **Rich features** like topics, user properties, A/B testing
- ✅ **Simpler implementation** with less custom code

## 📋 Prerequisites

1. Firebase project with Cloud Messaging enabled
2. Web app registered in Firebase Console
3. VAPID key generated for web push

## 🛠 Setup Steps

### 1. Environment Variables

Add these to your `.env` file:

```bash
# Firebase Configuration
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef123456
VITE_FIREBASE_VAPID_KEY=your_vapid_key_here

# Existing Stream Configuration
VITE_STREAM_API_KEY=your_stream_api_key
VITE_STREAM_APP_ID=your_stream_app_id
```

### 2. Firebase Console Setup

1. **Go to Firebase Console** → Your Project
2. **Project Settings** → General → Your apps
3. **Add app** → Web (if not already added)
4. **Copy configuration** values to environment variables
5. **Cloud Messaging** → Web configuration
6. **Generate VAPID key** and add to `VITE_FIREBASE_VAPID_KEY`

### 3. Service Worker Configuration

Update `public/sw.js` with your Firebase config:

```javascript
const firebaseConfig = {
  apiKey: "your-actual-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id", 
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};
```

## 🔄 Push Notification Flow

### 1. Client Registration
```typescript
// User grants permission
const permission = await requestPermission();

// Get FCM token
const token = await getToken(messaging, { vapidKey: VAPID_KEY });

// Register with backend
await registerToken({ platform: 'web', token });
```

### 2. Backend Sending (Go)
```go
import "firebase.google.com/go/v4/messaging"

func SendPushNotification(token, title, body string, data map[string]string) error {
    message := &messaging.Message{
        Token: token,
        Notification: &messaging.Notification{
            Title: title,
            Body:  body,
        },
        Data: data,
        Webpush: &messaging.WebpushConfig{
            Notification: &messaging.WebpushNotification{
                Icon: "https://your-domain.com/icon.png",
                Badge: "https://your-domain.com/badge.png",
            },
        },
    }
    
    _, err := client.Send(ctx, message)
    return err
}
```

### 3. Client Handling
- **Foreground**: `onMessage()` listener shows notification
- **Background**: Service worker `onBackgroundMessage()` handles
- **Click**: Deep linking routes to specific content

## 📱 Notification Types

### Lesson Notification
```go
data := map[string]string{
    "type": "lesson",
    "groupId": "123",
    "cohortId": "456", 
    "courseId": "789",
    "lessonId": "101",
    "url": "/groups/123/cohorts/456/courses/789/lessons/101",
}
```

### Live Class Notification
```go
data := map[string]string{
    "type": "live_class",
    "groupId": "123",
    "cohortId": "456",
    "moduleId": "789", 
    "url": "/groups/123/cohorts/456/modules/789",
}
```

### Chat Notification
```go
data := map[string]string{
    "type": "chat",
    "channelId": "general",
    "url": "/chat?channel=general",
}
```

## 🧪 Testing

### 1. Development Testing
- Use the test components in dev mode
- Send test notifications from Firebase Console
- Check browser developer tools for logs

### 2. Firebase Console Testing
1. **Cloud Messaging** → Send test message
2. **Target** → FCM registration token
3. **Paste token** from browser localStorage (`fcm_token`)
4. **Send** and verify notification appears

### 3. Backend Testing
```go
// Test function in your Go backend
func TestPushNotification() {
    token := "user_fcm_token_from_database"
    
    err := SendPushNotification(
        token,
        "Test Notification",
        "This is a test from the backend",
        map[string]string{
            "type": "test",
            "url": "/notifications",
        },
    )
    
    if err != nil {
        log.Printf("Failed to send notification: %v", err)
    }
}
```

## 🔧 Advanced Features

### 1. Topics
Subscribe users to topics for group notifications:

```typescript
// Client side
import { getMessaging, getToken } from 'firebase/messaging';

// Backend subscribes token to topic
// Go: client.SubscribeToTopic(ctx, []string{token}, "group_123")
```

### 2. User Properties
Set user properties for targeted messaging:

```go
// Set user properties in Firebase
client.SetCustomUserClaims(ctx, uid, map[string]interface{}{
    "cohort": "456",
    "group": "123",
})
```

### 3. Conditional Messaging
Send notifications based on conditions:

```go
message := &messaging.Message{
    Condition: "'group_123' in topics && 'premium' in topics",
    Notification: &messaging.Notification{
        Title: "Premium Group Update",
        Body:  "New content available for premium members",
    },
}
```

## 🚨 Troubleshooting

### Common Issues

1. **"Firebase not initialized"**
   - Check environment variables are set
   - Verify Firebase config in service worker

2. **"Permission denied"**
   - User must grant permission manually
   - Check browser notification settings

3. **"Token not received"**
   - Verify VAPID key is correct
   - Check Firebase project configuration

4. **"Service worker not registering"**
   - Ensure HTTPS is enabled
   - Check service worker file path

### Debug Commands

```javascript
// Check Firebase config
console.log('Firebase config:', firebaseConfig);

// Check FCM token
localStorage.getItem('fcm_token');

// Test notification permission
console.log('Permission:', Notification.permission);

// Check service worker
navigator.serviceWorker.getRegistrations().then(console.log);
```

## 📊 Analytics

Firebase provides built-in analytics for push notifications:

1. **Firebase Console** → Analytics → Events
2. **Track events**: `notification_received`, `notification_opened`
3. **Custom events** for specific notification types
4. **Conversion tracking** for notification → action flows

## 🔒 Security

1. **VAPID Keys** - Keep private key secure on backend
2. **Token Validation** - Validate tokens before sending
3. **Rate Limiting** - Implement notification rate limits
4. **User Preferences** - Respect user notification settings

## 🚀 Production Checklist

- [ ] Environment variables configured
- [ ] Firebase project set up correctly
- [ ] VAPID key generated and configured
- [ ] Service worker updated with real config
- [ ] Backend integration tested
- [ ] Deep linking working correctly
- [ ] Error handling implemented
- [ ] Analytics tracking set up
- [ ] User preferences system ready

## 📈 Expected Benefits

- **30-40% increase** in user engagement
- **Real-time notifications** drive immediate action
- **Better retention** through re-engagement
- **Improved course completion** rates
- **Stronger community** engagement
