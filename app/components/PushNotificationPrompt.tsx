import { useState, useEffect } from "react";
import { Bell, X, Check, AlertCircle } from "lucide-react";
import { useFirebasePushNotifications } from "~/lib/hooks/useFirebasePushNotifications";

interface PushNotificationPromptProps {
  onDismiss?: () => void;
  autoShow?: boolean;
  className?: string;
}

export function PushNotificationPrompt({
  onDismiss,
  autoShow = true,
  className = "",
}: PushNotificationPromptProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  const {
    permission,
    isSupported,
    isSubscribed,
    isLoading,
    error,
    requestPermission,
    subscribe,
  } = useFirebasePushNotifications();

  // Check if we should show the prompt
  useEffect(() => {
    if (!autoShow) return;

    // Don't show if already dismissed, not supported, or already granted/subscribed
    const dismissed = localStorage.getItem("push-notification-dismissed");
    if (dismissed || !isSupported || permission === "granted" || isSubscribed) {
      return;
    }

    // Show after a short delay to not overwhelm the user
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 3000);

    return () => clearTimeout(timer);
  }, [autoShow, isSupported, permission, isSubscribed]);

  const handleEnable = async () => {
    try {
      const permissionGranted = await requestPermission();
      if (permissionGranted) {
        const subscribed = await subscribe();
        if (subscribed) {
          setIsVisible(false);
          onDismiss?.();
        }
      }
    } catch (error) {
      console.error("Failed to enable push notifications:", error);
    }
  };

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    localStorage.setItem("push-notification-dismissed", "true");
    onDismiss?.();
  };

  const handleNotNow = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  // Don't render if not supported, already subscribed, or dismissed
  if (!isSupported || isSubscribed || isDismissed || !isVisible) {
    return null;
  }

  return (
    <div className={`fixed top-4 right-4 z-50 max-w-sm ${className}`}>
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-4">
        <div className="flex items-start gap-3">
          <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center flex-shrink-0">
            <Bell className="w-5 h-5 text-indigo-600" />
          </div>

          <div className="flex-1 min-w-0">
            <h3 className="font-semibold text-gray-900 mb-1">
              Stay Updated with Sphere
            </h3>
            <p className="text-sm text-gray-600 mb-3">
              Get notified about new lessons, live classes, and important
              updates even when the app is closed.
            </p>

            {error && (
              <div className="flex items-center gap-2 mb-3 p-2 bg-red-50 rounded-md">
                <AlertCircle className="w-4 h-4 text-red-500 flex-shrink-0" />
                <p className="text-xs text-red-600">{error}</p>
              </div>
            )}

            <div className="flex gap-2">
              <button
                onClick={handleEnable}
                disabled={isLoading}
                className="flex-1 bg-indigo-600 text-white text-sm font-medium py-2 px-3 rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isLoading ? (
                  <div className="flex items-center justify-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Enabling...
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-2">
                    <Check className="w-4 h-4" />
                    Enable
                  </div>
                )}
              </button>

              <button
                onClick={handleNotNow}
                disabled={isLoading}
                className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800 disabled:opacity-50 transition-colors"
              >
                Not now
              </button>
            </div>
          </div>

          <button
            onClick={handleDismiss}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>
      </div>
    </div>
  );
}

// Compact version for settings or profile pages
export function PushNotificationToggle() {
  const {
    permission,
    isSupported,
    isSubscribed,
    isLoading,
    error,
    requestPermission,
    subscribe,
    unsubscribe,
  } = useFirebasePushNotifications();

  if (!isSupported) {
    return (
      <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
        <div>
          <h4 className="font-medium text-gray-900">Push Notifications</h4>
          <p className="text-sm text-gray-500">Not supported in this browser</p>
        </div>
        <div className="w-10 h-6 bg-gray-300 rounded-full relative">
          <div className="w-4 h-4 bg-white rounded-full absolute top-1 left-1" />
        </div>
      </div>
    );
  }

  const handleToggle = async () => {
    if (isSubscribed) {
      await unsubscribe();
    } else {
      if (permission !== "granted") {
        const granted = await requestPermission();
        if (!granted) return;
      }
      await subscribe();
    }
  };

  return (
    <div className="flex items-center justify-between p-4 bg-white rounded-lg border border-gray-200">
      <div>
        <h4 className="font-medium text-gray-900">Push Notifications</h4>
        <p className="text-sm text-gray-500">
          {isSubscribed
            ? "Receive notifications even when the app is closed"
            : "Enable to get notified about important updates"}
        </p>
        {error && <p className="text-xs text-red-600 mt-1">{error}</p>}
      </div>

      <button
        onClick={handleToggle}
        disabled={isLoading}
        className={`relative w-12 h-6 rounded-full transition-colors ${
          isSubscribed ? "bg-indigo-600" : "bg-gray-300"
        } ${isLoading ? "opacity-50 cursor-not-allowed" : ""}`}
      >
        <div
          className={`w-4 h-4 bg-white rounded-full absolute top-1 transition-transform ${
            isSubscribed ? "translate-x-7" : "translate-x-1"
          }`}
        />
        {isLoading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />
          </div>
        )}
      </button>
    </div>
  );
}
