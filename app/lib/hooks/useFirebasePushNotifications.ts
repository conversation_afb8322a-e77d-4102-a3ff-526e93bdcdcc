import { useState, useEffect, useCallback } from "react";
import { getToken, onMessage } from "firebase/messaging";
import { getFirebaseMessaging, firebaseConfig } from "~/lib/firebase/config";
import { useRegisterPushNotificationToken } from "~/lib/api/client-queries";

export type PushPermissionState = "default" | "granted" | "denied";

export interface FirebasePushNotificationState {
  permission: PushPermissionState;
  isSupported: boolean;
  isSubscribed: boolean;
  isLoading: boolean;
  error: string | null;
  token: string | null;
}

export interface UseFirebasePushNotificationsReturn
  extends FirebasePushNotificationState {
  requestPermission: () => Promise<boolean>;
  subscribe: () => Promise<boolean>;
  unsubscribe: () => Promise<boolean>;
  refreshToken: () => Promise<void>;
}

// VAPID key for Firebase - get this from Firebase Console
const FIREBASE_VAPID_KEY = import.meta.env.VITE_FIREBASE_VAPID_KEY;

// Debug VAPID key loading
console.log(
  "[Debug] VAPID key loaded:",
  FIREBASE_VAPID_KEY ? "✅ Present" : "❌ Missing"
);
if (!FIREBASE_VAPID_KEY) {
  console.error(
    "[Debug] VITE_FIREBASE_VAPID_KEY environment variable is not set!"
  );
}

export function useFirebasePushNotifications(): UseFirebasePushNotificationsReturn {
  const [state, setState] = useState<FirebasePushNotificationState>({
    permission: "default",
    isSupported: false,
    isSubscribed: false,
    isLoading: false,
    error: null,
    token: null,
  });

  const { mutateAsync: registerToken } = useRegisterPushNotificationToken();

  const sendConfigToServiceWorker = useCallback(async () => {
    try {
      // Check if Firebase service worker is already registered
      let registration = await navigator.serviceWorker.getRegistration(
        "/firebase-messaging-sw.js"
      );

      if (!registration) {
        // Register Firebase messaging service worker
        registration = await navigator.serviceWorker.register(
          "/firebase-messaging-sw.js",
          {
            scope: "/",
          }
        );
        console.log("Firebase service worker registered");
      }

      await navigator.serviceWorker.ready;

      // Send config to Firebase service worker
      if (registration.active) {
        registration.active.postMessage({
          type: "FIREBASE_CONFIG",
          config: firebaseConfig,
        });
        console.log("Firebase config sent to Firebase service worker");
      } else {
        console.warn("Firebase service worker not active yet");
      }
    } catch (error) {
      console.error(
        "Failed to register or configure Firebase service worker:",
        error
      );
    }
  }, []);

  // Check if Firebase messaging is supported
  useEffect(() => {
    const checkSupport = async () => {
      try {
        const messaging = await getFirebaseMessaging();
        const isSupported = !!messaging;

        setState((prev) => ({
          ...prev,
          isSupported,
          permission: isSupported ? Notification.permission : "denied",
        }));

        if (isSupported) {
          await checkExistingToken();
          setupForegroundMessageListener();
          sendConfigToServiceWorker();
        }
      } catch (error) {
        console.error("Error checking Firebase messaging support:", error);
        setState((prev) => ({
          ...prev,
          isSupported: false,
          error: "Firebase messaging not supported",
        }));
      }
    };

    checkSupport();
  }, [sendConfigToServiceWorker]);

  const checkExistingToken = useCallback(async () => {
    try {
      const messaging = await getFirebaseMessaging();
      if (!messaging) return;

      // Check if we have a stored token
      const existingToken = localStorage.getItem("fcm_token");
      if (existingToken) {
        setState((prev) => ({
          ...prev,
          isSubscribed: true,
          token: existingToken,
        }));
      }
    } catch (error) {
      console.error("Error checking existing token:", error);
    }
  }, []);

  const setupForegroundMessageListener = useCallback(async () => {
    try {
      const messaging = await getFirebaseMessaging();
      if (!messaging) return;

      console.log("[Main App] Setting up foreground message listener");

      // Handle foreground messages
      onMessage(messaging, (payload) => {
        console.log("[Main App] Received foreground message:", payload);
        // Show notification manually when app is in foreground
        if (payload.notification) {
          console.log("[Main App] Showing foreground notification");
          new Notification(payload.notification.title || "Sphere", {
            body: payload.notification.body,
            icon: payload.notification.icon || "/favicon.ico",
            data: payload.data,
          });
        }
      });
    } catch (error) {
      console.error("Error setting up foreground message listener:", error);
    }
  }, []);

  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!state.isSupported) {
      setState((prev) => ({
        ...prev,
        error: "Push notifications not supported",
      }));
      return false;
    }

    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      const permission = await Notification.requestPermission();
      setState((prev) => ({ ...prev, permission, isLoading: false }));

      return permission === "granted";
    } catch (error) {
      console.error("Error requesting permission:", error);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: "Failed to request permission",
      }));
      return false;
    }
  }, [state.isSupported]);

  const subscribe = useCallback(async (): Promise<boolean> => {
    if (!state.isSupported || state.permission !== "granted") {
      console.error(
        "[Debug] Cannot subscribe - not supported or permission not granted",
        {
          isSupported: state.isSupported,
          permission: state.permission,
        }
      );
      return false;
    }

    console.log("[Debug] Starting push notification subscription...");
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Check VAPID key before proceeding
      if (!FIREBASE_VAPID_KEY) {
        throw new Error(
          "VAPID key is not configured. Please set VITE_FIREBASE_VAPID_KEY in your environment variables."
        );
      }

      console.log("[Debug] Getting Firebase messaging instance...");
      const messaging = await getFirebaseMessaging();
      if (!messaging) {
        throw new Error("Firebase messaging not available");
      }

      console.log("[Debug] Requesting FCM token with VAPID key...");
      // Get FCM token
      const token = await getToken(messaging, {
        vapidKey: FIREBASE_VAPID_KEY,
      });

      if (!token) {
        throw new Error(
          "Failed to get FCM token - this might be due to an invalid VAPID key"
        );
      }

      console.log(
        "[Debug] FCM token received successfully:",
        token.substring(0, 20) + "..."
      );

      // Register token with backend
      await registerToken({
        platform: "web",
        token: token,
      });

      // Store token locally
      localStorage.setItem("fcm_token", token);

      setState((prev) => ({
        ...prev,
        isSubscribed: true,
        token,
        isLoading: false,
      }));

      return true;
    } catch (error) {
      console.error("Error subscribing to push notifications:", error);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: "Failed to subscribe to notifications",
      }));
      return false;
    }
  }, [state.isSupported, state.permission, registerToken]);

  const unsubscribe = useCallback(async (): Promise<boolean> => {
    setState((prev) => ({ ...prev, isLoading: true, error: null }));

    try {
      // Remove token from local storage
      localStorage.removeItem("fcm_token");

      // Note: Firebase doesn't have a direct unsubscribe method
      // The backend should handle token invalidation

      setState((prev) => ({
        ...prev,
        isSubscribed: false,
        token: null,
        isLoading: false,
      }));

      return true;
    } catch (error) {
      console.error("Error unsubscribing from push notifications:", error);
      setState((prev) => ({
        ...prev,
        isLoading: false,
        error: "Failed to unsubscribe from notifications",
      }));
      return false;
    }
  }, []);

  const refreshToken = useCallback(async (): Promise<void> => {
    if (!state.isSupported || state.permission !== "granted") {
      return;
    }

    try {
      const messaging = await getFirebaseMessaging();
      if (!messaging) return;

      // Get a new token
      const newToken = await getToken(messaging, {
        vapidKey: FIREBASE_VAPID_KEY,
      });

      if (newToken && newToken !== state.token) {
        // Register new token with backend
        await registerToken({
          platform: "web",
          token: newToken,
        });

        // Update local storage
        localStorage.setItem("fcm_token", newToken);

        setState((prev) => ({
          ...prev,
          token: newToken,
        }));
      }
    } catch (error) {
      console.error("Error refreshing token:", error);
    }
  }, [state.isSupported, state.permission, state.token, registerToken]);

  return {
    ...state,
    requestPermission,
    subscribe,
    unsubscribe,
    refreshToken,
  };
}
