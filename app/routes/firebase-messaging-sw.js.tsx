import type { LoaderFunctionArgs } from "react-router";
import { readFileSync } from "fs";
import { join } from "path";

export async function loader({ request }: LoaderFunctionArgs) {
  try {
    // Read the Firebase service worker file from public directory
    const swPath = join(process.cwd(), "public", "firebase-messaging-sw.js");
    const swContent = readFileSync(swPath, "utf-8");

    return new Response(swContent, {
      status: 200,
      headers: {
        "Content-Type": "application/javascript",
        "Service-Worker-Allowed": "/",
        "Cache-Control": "no-cache, no-store, must-revalidate",
      },
    });
  } catch (error) {
    console.error("Failed to serve Firebase service worker:", error);
    return new Response("Service worker not found", { status: 404 });
  }
}
